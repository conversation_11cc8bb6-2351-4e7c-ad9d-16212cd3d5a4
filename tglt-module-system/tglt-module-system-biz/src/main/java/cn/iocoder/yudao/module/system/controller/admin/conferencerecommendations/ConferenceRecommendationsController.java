package cn.iocoder.yudao.module.system.controller.admin.conferencerecommendations;

import cn.iocoder.yudao.module.system.dal.dataobject.conferencetype.ConferenceTypeDO;
import cn.iocoder.yudao.module.system.dal.dataobject.suppliertype.SupplierTypeDO;
import cn.iocoder.yudao.module.system.dal.dataobject.supplychainrecommendations.SupplyChainRecommendationsDO;
import cn.iocoder.yudao.module.system.service.conferencetype.ConferenceTypeService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameters;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;


import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.system.controller.admin.conferencerecommendations.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.conferencerecommendations.ConferenceRecommendationsDO;
import cn.iocoder.yudao.module.system.service.conferencerecommendations.ConferenceRecommendationsService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "业务开发 - 会议推荐")
@RestController
@RequestMapping("/zcd/conference-recommendations")
@Validated
public class ConferenceRecommendationsController {
    @Resource
    private ConferenceTypeService conferenceTypeService;
    @Resource
    private ConferenceRecommendationsService conferenceRecommendationsService;

    @PostMapping("/create")
    @Operation(summary = "创建会议推荐")
    @PreAuthorize("@ss.hasPermission('zcd:conference-recommendations:create')")
    public CommonResult<Long> createConferenceRecommendations(@Valid @RequestBody ConferenceRecommendationsSaveReqVO createReqVO) {
        return success(conferenceRecommendationsService.createConferenceRecommendations(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新会议推荐")
    @PreAuthorize("@ss.hasPermission('zcd:conference-recommendations:update')")
    public CommonResult<Boolean> updateConferenceRecommendations(@Valid @RequestBody ConferenceRecommendationsSaveReqVO updateReqVO) {
        conferenceRecommendationsService.updateConferenceRecommendations(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会议推荐")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('zcd:conference-recommendations:delete')")
    public CommonResult<Boolean> deleteConferenceRecommendations(@RequestParam("id") Long id) {
        conferenceRecommendationsService.deleteConferenceRecommendations(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会议推荐")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('zcd:conference-recommendations:query')")
    public CommonResult<ConferenceRecommendationsRespVO> getConferenceRecommendations(@RequestParam("id") Long id) {
        ConferenceRecommendationsDO conferenceRecommendations = conferenceRecommendationsService.getConferenceRecommendations(id);
        Long conferenceTypeId = conferenceRecommendations.getConferenceTypeId();
        ConferenceTypeDO conferenceType = conferenceTypeService.getConferenceType(conferenceTypeId);
        conferenceRecommendations.setConferencetype(conferenceType);
        return success(BeanUtils.toBean(conferenceRecommendations, ConferenceRecommendationsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会议推荐分页")
    @PreAuthorize("@ss.hasPermission('zcd:conference-recommendations:query')")
    public CommonResult<PageResult<ConferenceRecommendationsRespVO>> getConferenceRecommendationsPage(@Valid ConferenceRecommendationsPageReqVO pageReqVO) {
        PageResult<ConferenceRecommendationsDO> pageResult = conferenceRecommendationsService.getConferenceRecommendationsPage(pageReqVO);
        if (pageResult!=null&& CollectionUtils.isNotEmpty(pageResult.getList())) {
            for (ConferenceRecommendationsDO conference : pageResult.getList()) {
                Long typeId = conference.getConferenceTypeId();
                ConferenceTypeDO conferenceType = conferenceTypeService.getConferenceType(typeId);
                conference.setConferencetype(conferenceType);
            }
            return success(BeanUtils.toBean(pageResult, ConferenceRecommendationsRespVO.class));
        }
        return success(BeanUtils.toBean(pageResult, ConferenceRecommendationsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会议推荐 Excel")
    @PreAuthorize("@ss.hasPermission('zcd:conference-recommendations:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportConferenceRecommendationsExcel(@Valid ConferenceRecommendationsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ConferenceRecommendationsDO> list = conferenceRecommendationsService.getConferenceRecommendationsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会议推荐.xls", "数据", ConferenceRecommendationsRespVO.class,
                        BeanUtils.toBean(list, ConferenceRecommendationsRespVO.class));
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得导入会议推荐模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<ConferenceRecommendationsImportExcelVO> list = Arrays.asList(
                ConferenceRecommendationsImportExcelVO.builder()
                        .conferenceType("普通会议")
                        .name("2024年技术创新大会")
                        .province("北京市")
                        .city("北京市")
                        .district("朝阳区")
                        .keyword("技术创新,数字化转型,人工智能")
                        .institutionName("创新科技协会")
                        .webUrl("http://www.conference1.com")
                        .email("<EMAIL>")
                        .phone("010-12345678")
                        .textIntroduction("面向技术管理者和开发人员")
                        .textContent("本次大会将围绕数字化转型主题...")
                        .serviceQuality("95")
                        .serviceAttitude("90")
                        .serviceSpeed("88")
                        .textDescription("年度重点技术交流活动")
                        .notes("需要提前两周报名")
                        .build(),
                ConferenceRecommendationsImportExcelVO.builder()
                        .conferenceType("特殊会议")
                        .name("2024年供应链管理峰会")
                        .province("上海市")
                        .city("上海市")
                        .district("浦东新区")
                        .keyword("供应链,智能制造,数字化")
                        .institutionName("供应链管理协会")
                        .webUrl("http://www.conference2.com")
                        .email("<EMAIL>")
                        .phone("021-87654321")
                        .textIntroduction("面向供应链管理者和行业专家")
                        .textContent("本次峰会将探讨智能供应链发展...")
                        .serviceQuality("92")
                        .serviceAttitude("94")
                        .serviceSpeed("91")
                        .textDescription("行业领先的供应链管理交流平台")
                        .notes("含国际演讲嘉宾")
                        .build()
        );

        // 输出
        ExcelUtils.write(response, "会议推荐导入模板.xls", "会议推荐", ConferenceRecommendationsImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入会议推荐")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('zcd:conference-recommendations:import')")
    public CommonResult<Map<String, Object>> importExcel(@RequestParam("file") MultipartFile file,
                                                     @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<ConferenceRecommendationsImportExcelVO> list = ExcelUtils.read(file, ConferenceRecommendationsImportExcelVO.class);
        return success(conferenceRecommendationsService.importConferenceRecommendations(list, updateSupport));
    }

}