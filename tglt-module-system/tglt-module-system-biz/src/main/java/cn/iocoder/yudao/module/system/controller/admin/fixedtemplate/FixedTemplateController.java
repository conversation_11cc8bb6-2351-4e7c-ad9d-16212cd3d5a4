package cn.iocoder.yudao.module.system.controller.admin.fixedtemplate;

import cn.iocoder.yudao.module.system.controller.admin.fixedtemplate.vo.FixedTemplatePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.fixedtemplate.vo.FixedTemplateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.fixedtemplate.vo.FixedTemplateSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.fixedtemplate.FixedTemplateDO;
import cn.iocoder.yudao.module.system.dal.dataobject.fixedtemplate.FixedTemplateFileDO;
import cn.iocoder.yudao.module.system.service.fixedtemplate.FixedTemplateService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 固定模板")
@RestController
@RequestMapping("/zcd/fixed-template")
@Validated
public class FixedTemplateController {

    @Resource
    private FixedTemplateService fixedTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建固定模板")
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template:create')")
    public CommonResult<Long> createFixedTemplate(@Valid @RequestBody FixedTemplateSaveReqVO createReqVO) {
        return success(fixedTemplateService.createFixedTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新固定模板")
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template:update')")
    public CommonResult<Boolean> updateFixedTemplate(@Valid @RequestBody FixedTemplateSaveReqVO updateReqVO) {
        fixedTemplateService.updateFixedTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除固定模板")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template:delete')")
    public CommonResult<Boolean> deleteFixedTemplate(@RequestParam("id") Long id) {
        fixedTemplateService.deleteFixedTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得固定模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template:query')")
    public CommonResult<FixedTemplateRespVO> getFixedTemplate(@RequestParam("id") Long id) {
        FixedTemplateDO fixedTemplate = fixedTemplateService.getFixedTemplate(id);
        return success(BeanUtils.toBean(fixedTemplate, FixedTemplateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得固定模板分页")
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template:query')")
    public CommonResult<PageResult<FixedTemplateRespVO>> getFixedTemplatePage(@Valid FixedTemplatePageReqVO pageReqVO) {
        PageResult<FixedTemplateDO> pageResult = fixedTemplateService.getFixedTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FixedTemplateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出固定模板 Excel")
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportFixedTemplateExcel(@Valid FixedTemplatePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<FixedTemplateDO> list = fixedTemplateService.getFixedTemplatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "固定模板.xls", "数据", FixedTemplateRespVO.class,
                        BeanUtils.toBean(list, FixedTemplateRespVO.class));
    }

    // ==================== 子表（固定模板关联文件） ====================

    @GetMapping("/fixed-template-file/list-by-template-id")
    @Operation(summary = "获得固定模板关联文件列表")
    @Parameter(name = "templateId", description = "关联的模板ID")
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template:query')")
    public CommonResult<List<FixedTemplateFileDO>> getFixedTemplateFileListByTemplateId(@RequestParam("templateId") Long templateId) {
        return success(fixedTemplateService.getFixedTemplateFileListByTemplateId(templateId));
    }

    // ==================== 子表（固定模板关联文件） 单独增删改查 ====================

    /**
     * 创建单个固定模板关联文件
     * @param file 固定模板关联文件信息
     * @return 创建的文件 ID
     */
    @PostMapping("/createSingleFixedTemplateFile")
    @Operation(summary = "创建单个固定模板关联文件")
    @Parameter(name = "templateId", description = "主表固定模板 ID", required = true)
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template-file:create')")
    public CommonResult<Long> createSingleFixedTemplateFile(
            @Valid @RequestBody FixedTemplateFileDO file) {
        return success(fixedTemplateService.createSingleFixedTemplateFile(file));
    }

    /**
     * 更新单个固定模板关联文件
     * @param file 固定模板关联文件信息，包含文件 ID
     * @return 操作结果
     */
    @PostMapping("/updateSingleFixedTemplateFile")
    @Operation(summary = "更新单个固定模板关联文件")
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template-file:update')")
    public CommonResult<Boolean> updateSingleFixedTemplateFile(
            @Valid @RequestBody FixedTemplateFileDO file) {
        fixedTemplateService.updateSingleFixedTemplateFile(file);
        return success(true);
    }

    /**
     * 删除单个固定模板关联文件
     * @param fileId 固定模板关联文件 ID
     * @return 操作结果
     */
    @PostMapping("/deleteSingleFixedTemplateFile")
    @Operation(summary = "删除单个固定模板关联文件")
    @Parameter(name = "fileId", description = "固定模板关联文件 ID", required = true)
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template-file:delete')")
    public CommonResult<Boolean> deleteSingleFixedTemplateFile(
            @RequestParam("fileId") Long fileId) {
        fixedTemplateService.deleteSingleFixedTemplateFile(fileId);
        return success(true);
    }

    /**
     * 获取单个固定模板关联文件
     * @param fileId 固定模板关联文件 ID
     * @return 固定模板关联文件信息
     */
    @GetMapping("/getSingleFixedTemplateFile")
    @Operation(summary = "获取单个固定模板关联文件")
    @Parameter(name = "fileId", description = "固定模板关联文件 ID", required = true)
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template-file:query')")
    public CommonResult<FixedTemplateFileDO> getSingleFixedTemplateFile(
            @RequestParam("fileId") Long fileId) {
        return success(fixedTemplateService.getFixedTemplateFileById(fileId));
    }

    @GetMapping("/merge-word-files")
    @Operation(summary = "合并固定模板关联的Word文件")
    @Parameter(name = "templateId", description = "关联的模板ID", required = true)
    @PreAuthorize("@ss.hasPermission('zcd:fixed-template:query')")
    public void mergeWordFilesByTemplateId(@RequestParam("templateId") Long templateId, HttpServletResponse response) {
        fixedTemplateService.mergeWordFilesByTemplateId(templateId, response);
    }
}