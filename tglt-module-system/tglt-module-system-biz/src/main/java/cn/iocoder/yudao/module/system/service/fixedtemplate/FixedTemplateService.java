package cn.iocoder.yudao.module.system.service.fixedtemplate;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.module.system.controller.admin.fixedtemplate.vo.FixedTemplatePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.fixedtemplate.vo.FixedTemplateSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.fixedtemplate.FixedTemplateDO;
import cn.iocoder.yudao.module.system.dal.dataobject.fixedtemplate.FixedTemplateFileDO;

/**
 * 固定模板 Service 接口
 *
 * <AUTHOR>
 */
public interface FixedTemplateService {

    /**
     * 创建固定模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFixedTemplate(@Valid FixedTemplateSaveReqVO createReqVO);

    /**
     * 更新固定模板
     *
     * @param updateReqVO 更新信息
     */
    void updateFixedTemplate(@Valid FixedTemplateSaveReqVO updateReqVO);

    /**
     * 删除固定模板
     *
     * @param id 编号
     */
    void deleteFixedTemplate(Long id);

    /**
     * 获得固定模板
     *
     * @param id 编号
     * @return 固定模板
     */
    FixedTemplateDO getFixedTemplate(Long id);

    /**
     * 获得固定模板分页
     *
     * @param pageReqVO 分页查询
     * @return 固定模板分页
     */
    PageResult<FixedTemplateDO> getFixedTemplatePage(FixedTemplatePageReqVO pageReqVO);

    // ==================== 子表（固定模板关联文件） ====================

    /**
     * 获得固定模板关联文件列表
     *
     * @param templateId 关联的模板ID
     * @return 固定模板关联文件列表
     */

    List<FixedTemplateFileDO> getFixedTemplateFileListByTemplateId(Long templateId);


    FixedTemplateFileDO getFixedTemplateFileById(Long fileId);


    Long createSingleFixedTemplateFile(FixedTemplateFileDO file);

    void updateSingleFixedTemplateFile(FixedTemplateFileDO file);

    void deleteSingleFixedTemplateFile(Long fileId);

    /**
     * 合并固定模板关联的Word文件
     *
     * @param templateId 关联的模板ID
     * @param response HTTP响应对象
     */
    void mergeWordFilesByTemplateId(Long templateId, HttpServletResponse response);


}