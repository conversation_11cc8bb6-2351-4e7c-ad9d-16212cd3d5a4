package cn.iocoder.yudao.module.system.service.deliveryinfo;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.system.controller.admin.deliveryinfo.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.deliveryinfo.DeliveryInfoDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 交付信息 Service 接口
 *
 * <AUTHOR>
 */
public interface DeliveryInfoService {

    /**
     * 创建交付信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeliveryInfo(@Valid DeliveryInfoSaveReqVO createReqVO);

    /**
     * 更新交付信息
     *
     * @param updateReqVO 更新信息
     */
    void updateDeliveryInfo(@Valid DeliveryInfoSaveReqVO updateReqVO);

    /**
     * 删除交付信息
     *
     * @param id 编号
     */
    void deleteDeliveryInfo(Long id);

    /**
     * 获得交付信息
     *
     * @param id 编号
     * @return 交付信息
     */
    DeliveryInfoDO getDeliveryInfo(Long id);

    /**
     * 获得交付信息（包含文件列表转换）
     *
     * @param id 编号
     * @return 交付信息VO
     */
    DeliveryInfoRespVO getDeliveryInfoWithFileList(Long id);

    /**
     * 获得交付信息分页
     *
     * @param pageReqVO 分页查询
     * @return 交付信息分页
     */
    PageResult<DeliveryInfoDO> getDeliveryInfoPage(DeliveryInfoPageReqVO pageReqVO);

    /**
     * 获得交付信息分页（包含文件列表转换）
     *
     * @param pageReqVO 分页查询
     * @return 交付信息分页VO
     */
    PageResult<DeliveryInfoRespVO> getDeliveryInfoPageWithFileList(DeliveryInfoPageReqVO pageReqVO);

    /**
     * 获得项目信息与时间节点关联数据
     *
     * @return 项目信息与时间节点关联数据
     */
    List<ProjectInfoWithTimeNodeRespVO> getProjectInfoWithTimeNodes();

    /**
     * 获得项目编号、项目名称、节点名称、节点编号四个独立列表
     *
     * @param customerName 客户名称，用于过滤。如果为空，则返回所有数据
     * @return 四个独立列表数据
     */
    ProjectTimeNodeListsRespVO getProjectTimeNodeLists(String customerName);

    /**
     * 获得客户对应的项目数据
     *
     * @return 客户项目关联数据
     */
    List<CustomerProjectRespVO> getCustomerProjectData();

    /**
     * 获得所有项目列表
     *
     * @return 项目列表数据
     */
    List<CustomerProjectRespVO.ProjectInfo> getProjectList();

}