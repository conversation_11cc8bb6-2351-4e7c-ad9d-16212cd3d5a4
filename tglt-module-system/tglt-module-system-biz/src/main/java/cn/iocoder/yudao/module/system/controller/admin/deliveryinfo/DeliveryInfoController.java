package cn.iocoder.yudao.module.system.controller.admin.deliveryinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import cn.iocoder.yudao.module.system.controller.admin.deliveryinfo.vo.*;
import cn.iocoder.yudao.module.system.service.deliveryinfo.DeliveryInfoService;

import java.util.stream.Collectors;

@Tag(name = "业务开发 - 项目管理板块-项目交付-全客户项目交付/项目交付列表信息")
@RestController
@RequestMapping("/system/delivery-info")
@Validated
public class DeliveryInfoController {

    @Resource
    private DeliveryInfoService deliveryInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建交付信息")
    @PreAuthorize("@ss.hasPermission('system:delivery-info:create')")
    public CommonResult<Long> createDeliveryInfo(@Valid @RequestBody DeliveryInfoSaveReqVO createReqVO) {
        return success(deliveryInfoService.createDeliveryInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新交付信息")
    @PreAuthorize("@ss.hasPermission('system:delivery-info:update')")
    public CommonResult<Boolean> updateDeliveryInfo(@Valid @RequestBody DeliveryInfoSaveReqVO updateReqVO) {
        deliveryInfoService.updateDeliveryInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除交付信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:delivery-info:delete')")
    public CommonResult<Boolean> deleteDeliveryInfo(@RequestParam("id") Long id) {
        deliveryInfoService.deleteDeliveryInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得交付信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:delivery-info:query')")
    public CommonResult<DeliveryInfoRespVO> getDeliveryInfo(@RequestParam("id") Long id) {
        DeliveryInfoRespVO deliveryInfo = deliveryInfoService.getDeliveryInfoWithFileList(id);
        return success(deliveryInfo);
    }

    @GetMapping("/page")
    @Operation(summary = "获得交付信息分页")
    @PreAuthorize("@ss.hasPermission('system:delivery-info:query')")
    public CommonResult<PageResult<DeliveryInfoRespVO>> getDeliveryInfoPage(@Valid DeliveryInfoPageReqVO pageReqVO) {
        if (Objects.equals(pageReqVO.getCustomerName(),pageReqVO.getProjectName())){
            pageReqVO.setProjectName(null);
        }
        PageResult<DeliveryInfoRespVO> pageResult = deliveryInfoService.getDeliveryInfoPageWithFileList(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出交付信息 Excel")
    @PreAuthorize("@ss.hasPermission('system:delivery-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDeliveryInfoExcel(@Valid DeliveryInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<DeliveryInfoRespVO> pageResult = deliveryInfoService.getDeliveryInfoPageWithFileList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "交付信息.xls", "数据", DeliveryInfoRespVO.class, pageResult.getList());
    }

    @GetMapping("/project-info-with-time-nodes")
    @Operation(summary = "获得项目信息与时间节点关联数据")
    @PreAuthorize("@ss.hasPermission('system:delivery-info:query')")
    public CommonResult<ProjectTimeNodeListsRespVO> getProjectInfoWithTimeNodes(
            @RequestParam(value = "customerName", required = false) String customerName) {
        ProjectTimeNodeListsRespVO result = deliveryInfoService.getProjectTimeNodeLists(customerName);
        if (result == null) {
            return success(new ProjectTimeNodeListsRespVO());
        }

        // 确保列表不为空
        if (result.getProjectList() == null) {
            result.setProjectList(new ArrayList<>());
        }
        if (result.getNodeList() == null) {
            result.setNodeList(new ArrayList<>());
        }

        return success(result);
    }

    @GetMapping("/customer-project-data")
    @Operation(summary = "获得全客户对应的项目数据")
    @PreAuthorize("@ss.hasPermission('system:delivery-info:query')")
    public CommonResult<List<CustomerProjectRespVO>> getCustomerProjectData() {
        List<CustomerProjectRespVO> result = deliveryInfoService.getCustomerProjectData();
        return success(result);
    }

    @GetMapping("/project-list")
    @Operation(summary = "获得甲方端项目列表数据")
    @PreAuthorize("@ss.hasPermission('system:delivery-info:query')")
    public CommonResult<List<CustomerProjectRespVO.ProjectInfo>> getProjectList() {
        List<CustomerProjectRespVO.ProjectInfo> result = deliveryInfoService.getProjectList();
        return success(result);
    }

}
