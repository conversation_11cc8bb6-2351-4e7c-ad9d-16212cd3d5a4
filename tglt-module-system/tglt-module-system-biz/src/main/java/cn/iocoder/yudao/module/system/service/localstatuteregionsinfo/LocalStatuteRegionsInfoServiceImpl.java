package cn.iocoder.yudao.module.system.service.localstatuteregionsinfo;

import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.module.system.controller.admin.district.vo.DistrictTreeNodeVO;
import cn.iocoder.yudao.module.system.dal.dataobject.district.DistrictDO;
import cn.iocoder.yudao.module.system.dal.mysql.district.DistrictMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.system.controller.admin.localstatuteregionsinfo.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.localstatuteregionsinfo.LocalStatuteRegionsInfoDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import cn.iocoder.yudao.module.system.dal.mysql.localstatuteregionsinfo.LocalStatuteRegionsInfoMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;

/**
 * 本地法规地域列 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LocalStatuteRegionsInfoServiceImpl implements LocalStatuteRegionsInfoService {

    @Resource
    private LocalStatuteRegionsInfoMapper localStatuteRegionsInfoMapper;

    @Resource
    private DistrictMapper districtMapper;

    @Override
    public Long createLocalStatuteRegionsInfo(LocalStatuteRegionsInfoSaveReqVO createReqVO) {
        // 插入
        LocalStatuteRegionsInfoDO localStatuteRegionsInfo = BeanUtils.toBean(createReqVO, LocalStatuteRegionsInfoDO.class);
        // 处理regions字段
        if (createReqVO.getRegions() != null) {
            localStatuteRegionsInfo.setRegions(com.alibaba.fastjson.JSON.toJSONString(createReqVO.getRegions()));
        }
        // 处理regionId字段
        if (StringUtils.isNotBlank(createReqVO.getRegionId())) {
            try {
                localStatuteRegionsInfo.setRegionId(Long.parseLong(createReqVO.getRegionId()));
            } catch (NumberFormatException e) {
                throw exception(GlobalErrorCodeConstants.BAD_REQUEST, "区域ID格式不正确");
            }
        }
        //主键id设置为null走数据库序列自增
        localStatuteRegionsInfo.setId(null);
        localStatuteRegionsInfoMapper.insert(localStatuteRegionsInfo);
        // 返回
        return localStatuteRegionsInfo.getId();
    }

    @Override
    public void updateLocalStatuteRegionsInfo(LocalStatuteRegionsInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateLocalStatuteRegionsInfoExists(updateReqVO.getId());
        // 更新
        LocalStatuteRegionsInfoDO updateObj = BeanUtils.toBean(updateReqVO, LocalStatuteRegionsInfoDO.class);
        // 处理regions字段
        if (updateReqVO.getRegions() != null) {
            updateObj.setRegions(com.alibaba.fastjson.JSON.toJSONString(updateReqVO.getRegions()));
        }
        // 处理regionId字段
        if (StringUtils.isNotBlank(updateReqVO.getRegionId())) {
            try {
                updateObj.setRegionId(Long.parseLong(updateReqVO.getRegionId()));
            } catch (NumberFormatException e) {
                throw exception(GlobalErrorCodeConstants.BAD_REQUEST, "区域ID格式不正确");
            }
        }
        localStatuteRegionsInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteLocalStatuteRegionsInfo(Long id) {
        // 校验存在
        validateLocalStatuteRegionsInfoExists(id);
        // 删除
        localStatuteRegionsInfoMapper.deleteById(id);
    }

    private void validateLocalStatuteRegionsInfoExists(Long id) {
        if (localStatuteRegionsInfoMapper.selectById(id) == null) {
            throw exception(GlobalErrorCodeConstants.LOCAL_STATUTE_REGIONS_INFO_NOT_EXISTS);
        }
    }

    @Override
    public LocalStatuteRegionsInfoDO getLocalStatuteRegionsInfo(Long id) {
        return localStatuteRegionsInfoMapper.selectById(id);
    }

    @Override
    public PageResult<LocalStatuteRegionsInfoDO> getLocalStatuteRegionsInfoPage(LocalStatuteRegionsInfoPageReqVO pageReqVO) {
        return localStatuteRegionsInfoMapper.selectPage(pageReqVO);
    }


    @Override
    public List<LocalStatuteRegionsInfoDO> searchLocalStatuteRegionsInfo(String keyword) {
        return localStatuteRegionsInfoMapper.selectListByKeyword(keyword);
    }

    @Override
    public Long createLocalStatuteRegionsInfoNew(LocalStatuteRegionsInfoSaveReqVO createReqVO) {
        // 新需求：根据一级地域、二级地域、备注创建
        LocalStatuteRegionsInfoDO localStatuteRegionsInfo = new LocalStatuteRegionsInfoDO();

        // 设置新字段
        localStatuteRegionsInfo.setFirstLevelRegion(createReqVO.getFirstLevelRegion());
        localStatuteRegionsInfo.setSecondLevelRegion(createReqVO.getSecondLevelRegion());
        localStatuteRegionsInfo.setRemarks(createReqVO.getRemarks());

        // 设置其他必要字段
        localStatuteRegionsInfo.setStatuteCode(createReqVO.getStatuteCode());
        localStatuteRegionsInfo.setStatuteName(createReqVO.getStatuteName());
        localStatuteRegionsInfo.setTenantId(createReqVO.getTenantId());
        localStatuteRegionsInfo.setDeptId(createReqVO.getDeptId());
        localStatuteRegionsInfo.setStatus(0); // 默认正常状态

        // 主键id设置为null走数据库序列自增
        localStatuteRegionsInfo.setId(null);
        localStatuteRegionsInfoMapper.insert(localStatuteRegionsInfo);

        return localStatuteRegionsInfo.getId();
    }

    @Override
    public void updateLocalStatuteRegionsInfoNew(LocalStatuteRegionsInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateLocalStatuteRegionsInfoExists(updateReqVO.getId());

        // 新需求：根据一级地域、二级地域、备注更新
        LocalStatuteRegionsInfoDO updateObj = new LocalStatuteRegionsInfoDO();
        updateObj.setId(updateReqVO.getId());

        // 设置新字段
        updateObj.setFirstLevelRegion(updateReqVO.getFirstLevelRegion());
        updateObj.setSecondLevelRegion(updateReqVO.getSecondLevelRegion());
        updateObj.setRemarks(updateReqVO.getRemarks());

        // 设置其他必要字段
        updateObj.setStatuteCode(updateReqVO.getStatuteCode());
        updateObj.setStatuteName(updateReqVO.getStatuteName());
        updateObj.setTenantId(updateReqVO.getTenantId());
        updateObj.setDeptId(updateReqVO.getDeptId());

        localStatuteRegionsInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteLocalStatuteRegionsInfoNew(Long id) {
        // 新需求：删除逻辑保持不变
        validateLocalStatuteRegionsInfoExists(id);
        localStatuteRegionsInfoMapper.deleteById(id);
    }

    @Override
    public LocalStatuteRegionsInfoRespVO getLocalStatuteRegionsInfoNew(Long id) {
        // 新需求：返回包含一级地域、二级地域级联的数据
        LocalStatuteRegionsInfoDO localStatuteRegionsInfo = localStatuteRegionsInfoMapper.selectById(id);
        if (localStatuteRegionsInfo == null) {
            throw exception(GlobalErrorCodeConstants.LOCAL_STATUTE_REGIONS_INFO_NOT_EXISTS);
        }

        LocalStatuteRegionsInfoRespVO respVO = BeanUtils.toBean(localStatuteRegionsInfo, LocalStatuteRegionsInfoRespVO.class);

        // 处理regions字段
        if (localStatuteRegionsInfo.getRegions() != null) {
            respVO.setRegions(com.alibaba.fastjson.JSON.parseArray(localStatuteRegionsInfo.getRegions(), String.class));
        }

        // 构建级联地域信息
        buildCascadeRegionInfo(respVO, localStatuteRegionsInfo);

        return respVO;
    }

    @Override
    public PageResult<LocalStatuteRegionsInfoRespVO> getLocalStatuteRegionsInfoPageNew(LocalStatuteRegionsInfoPageReqVO pageReqVO) {
        // 新需求：返回包含一级地域、二级地域级联的分页数据
        PageResult<LocalStatuteRegionsInfoDO> pageResult = localStatuteRegionsInfoMapper.selectPage(pageReqVO);

        List<LocalStatuteRegionsInfoRespVO> respVOList = new ArrayList<>();
        for (LocalStatuteRegionsInfoDO localStatuteRegionsInfo : pageResult.getList()) {
            LocalStatuteRegionsInfoRespVO respVO = BeanUtils.toBean(localStatuteRegionsInfo, LocalStatuteRegionsInfoRespVO.class);

            // 处理regions字段
            if (localStatuteRegionsInfo.getRegions() != null) {
                respVO.setRegions(com.alibaba.fastjson.JSON.parseArray(localStatuteRegionsInfo.getRegions(), String.class));
            }

            // 构建级联地域信息
            buildCascadeRegionInfo(respVO, localStatuteRegionsInfo);

            respVOList.add(respVO);
        }

        return new PageResult<>(respVOList, pageResult.getTotal());
    }

    /**
     * 构建级联地域信息
     * @param respVO 响应VO
     * @param localStatuteRegionsInfo 数据库对象
     */
    private void buildCascadeRegionInfo(LocalStatuteRegionsInfoRespVO respVO, LocalStatuteRegionsInfoDO localStatuteRegionsInfo) {
        // 如果有二级地域，构建级联结构
        if (StringUtils.isNotBlank(localStatuteRegionsInfo.getSecondLevelRegion())) {
            // 创建一级地域节点
            DistrictTreeNodeVO firstLevelNode = new DistrictTreeNodeVO();
            firstLevelNode.setId(1L); // 临时ID
            firstLevelNode.setName(localStatuteRegionsInfo.getFirstLevelRegion());
            firstLevelNode.setLevel(1);

            // 创建二级地域节点
            DistrictTreeNodeVO secondLevelNode = new DistrictTreeNodeVO();
            secondLevelNode.setId(2L); // 临时ID
            secondLevelNode.setName(localStatuteRegionsInfo.getSecondLevelRegion());
            secondLevelNode.setLevel(2);
            secondLevelNode.setChildren(new ArrayList<>());

            // 设置级联关系
            firstLevelNode.setChildren(Collections.singletonList(secondLevelNode));
            firstLevelNode.setStatuteCode(localStatuteRegionsInfo.getStatuteCode());

            respVO.setDistrictTreeNodeVO(firstLevelNode);
        } else {
            // 只有一级地域
            DistrictTreeNodeVO firstLevelNode = new DistrictTreeNodeVO();
            firstLevelNode.setId(1L); // 临时ID
            firstLevelNode.setName(localStatuteRegionsInfo.getFirstLevelRegion());
            firstLevelNode.setLevel(1);
            firstLevelNode.setChildren(new ArrayList<>());
            firstLevelNode.setStatuteCode(localStatuteRegionsInfo.getStatuteCode());

            respVO.setDistrictTreeNodeVO(firstLevelNode);
        }
    }

}
