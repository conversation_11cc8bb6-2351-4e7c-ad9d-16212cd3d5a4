package cn.iocoder.yudao.module.system.service.fixedtemplate;

import cn.iocoder.yudao.module.system.controller.admin.fixedtemplate.vo.FixedTemplatePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.fixedtemplate.vo.FixedTemplateSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.fixedtemplate.FixedTemplateDO;
import cn.iocoder.yudao.module.system.dal.dataobject.fixedtemplate.FixedTemplateFileDO;
import cn.iocoder.yudao.module.system.dal.mysql.fixedtemplate.FixedTemplateFileMapper;
import cn.iocoder.yudao.module.system.dal.mysql.fixedtemplate.FixedTemplateMapper;
import org.springframework.stereotype.Service;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.Paragraph;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.hwpf.usermodel.Table;
import org.apache.poi.hwpf.usermodel.TableCell;
import org.apache.poi.hwpf.usermodel.TableIterator;
import org.apache.poi.hwpf.usermodel.TableRow;
import org.apache.poi.xwpf.usermodel.BreakType;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;

import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 * 固定模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FixedTemplateServiceImpl implements FixedTemplateService {

    @Resource
    private FixedTemplateMapper fixedTemplateMapper;
    @Resource
    private FixedTemplateFileMapper fixedTemplateFileMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createFixedTemplate(FixedTemplateSaveReqVO createReqVO) {
        // 插入
        FixedTemplateDO fixedTemplate = BeanUtils.toBean(createReqVO, FixedTemplateDO.class);
        fixedTemplateMapper.insert(fixedTemplate);
        // 返回
        return fixedTemplate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFixedTemplate(FixedTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateFixedTemplateExists(updateReqVO.getId());
        // 更新
        FixedTemplateDO updateObj = BeanUtils.toBean(updateReqVO, FixedTemplateDO.class);
        fixedTemplateMapper.updateById(updateObj);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFixedTemplate(Long id) {
        // 校验存在
        validateFixedTemplateExists(id);
        // 删除
        fixedTemplateMapper.deleteById(id);

        // 删除子表
        //deleteFixedTemplateFileByTemplateId(id);
    }

    private void validateFixedTemplateExists(Long id) {
        if (fixedTemplateMapper.selectById(id) == null) {
            throw new RuntimeException("固定模板不存在，ID: " + id);
        }
    }

    @Override
    public FixedTemplateDO getFixedTemplate(Long id) {
        return fixedTemplateMapper.selectById(id);
    }

    @Override
    public PageResult<FixedTemplateDO> getFixedTemplatePage(FixedTemplatePageReqVO pageReqVO) {
        return fixedTemplateMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（固定模板关联文件） ====================

    @Override
    public List<FixedTemplateFileDO> getFixedTemplateFileListByTemplateId(Long templateId) {
        return fixedTemplateFileMapper.selectListByTemplateId(templateId);
    }


    // 实现通过文件 ID 获取固定模板关联文件的方法
    @Override
    public FixedTemplateFileDO getFixedTemplateFileById(Long fileId) {
        return fixedTemplateFileMapper.selectById(fileId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSingleFixedTemplateFile(FixedTemplateFileDO file) {
        fixedTemplateFileMapper.insert(file);
        return file.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSingleFixedTemplateFile(FixedTemplateFileDO file) {
        // 校验文件是否存在
        if (fixedTemplateFileMapper.selectById(file.getId()) == null) {
            throw new RuntimeException("固定模板文件不存在，ID: " + file.getId());
        }
        fixedTemplateFileMapper.updateById(file);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSingleFixedTemplateFile(Long fileId) {
        // 校验文件是否存在
        if (fixedTemplateFileMapper.selectById(fileId) == null) {
            throw new RuntimeException("固定模板文件不存在，ID: " + fileId);
        }
        fixedTemplateFileMapper.deleteById(fileId);
    }


    @Override
    public void mergeWordFilesByTemplateId(Long templateId, HttpServletResponse response) {
        List<FixedTemplateFileDO> fileList = getFixedTemplateFileListByTemplateId(templateId);

        if (CollectionUtils.isEmpty(fileList)) {
            throw new RuntimeException("未找到关联的Word文件，无法进行合并");
        }

        try (XWPFDocument mergedDoc = new XWPFDocument()) {
            for (int i = 0; i < fileList.size(); i++) {
                FixedTemplateFileDO fileDO = fileList.get(i);
                String filePath = fileDO.getFilePath();
                String fileName = fileDO.getFileName();

                try {
                    URL url = new URL(filePath);
                    URLConnection connection = url.openConnection();
                    connection.setConnectTimeout(10000);
                    connection.setReadTimeout(30000);

                    try (InputStream inputStream = connection.getInputStream()) {
                        String lowerPath = filePath.toLowerCase();

                        if (lowerPath.endsWith(".docx")) {
                            try (XWPFDocument subDoc = new XWPFDocument(inputStream)) {
                                copyDocumentContent(subDoc, mergedDoc);
                            }
                        } else if (lowerPath.endsWith(".doc")) {
                            // 改进的.doc文件处理逻辑
                            try {
                                // 为.doc文件创建新的连接，确保输入流是全新的
                                URLConnection docConnection = url.openConnection();
                                docConnection.setConnectTimeout(10000);
                                docConnection.setReadTimeout(30000);

                                try (InputStream docStream = docConnection.getInputStream();
                                     HWPFDocument hwpfDoc = new HWPFDocument(docStream)) {

                                    // 通过Range获取所有内容
                                    Range range = hwpfDoc.getRange();
                                    int numParagraphs = range.numParagraphs();

                                    // 逐段处理文本内容
                                    for (int p = 0; p < numParagraphs; p++) {
                                        Paragraph para = range.getParagraph(p);
                                        String text = para.text();
                                        if (text != null && !text.trim().isEmpty()) {
                                            // 清除特殊标记和多余空白
                                            text = text.replaceAll("\\r|\\n", " ").trim()
                                                    .replaceAll(" +", " ");
                                            XWPFParagraph newPara = mergedDoc.createParagraph();
                                            XWPFRun run = newPara.createRun();
                                            run.setText(text);
                                        }
                                    }

                                    // 处理表格内容（如果有）
                                    try {
                                        TableIterator tableIter = new TableIterator(range);
                                        while (tableIter.hasNext()) {
                                            Table docTable = tableIter.next();
                                            // 创建新表格，设置行列数
                                            XWPFTable newTable = mergedDoc.createTable(
                                                    docTable.numRows(),
                                                    docTable.getRow(0).numCells()
                                            );

                                            // 复制表格内容
                                            for (int r = 0; r < docTable.numRows(); r++) {
                                                TableRow sourceRow = docTable.getRow(r);
                                                XWPFTableRow targetRow = newTable.getRow(r);

                                                for (int c = 0; c < sourceRow.numCells(); c++) {
                                                    TableCell sourceCell = sourceRow.getCell(c);
                                                    XWPFTableCell targetCell = targetRow.getCell(c);

                                                    // 清除目标单元格默认段落
                                                    if (targetCell.getParagraphs().size() > 0) {
                                                        targetCell.removeParagraph(0);
                                                    }

                                                    // 添加单元格内容
                                                    XWPFParagraph cellPara = targetCell.addParagraph();
                                                    XWPFRun cellRun = cellPara.createRun();
                                                    cellRun.setText(sourceCell.text().trim()
                                                            .replaceAll("\\r|\\n", " ").trim());
                                                }
                                            }

                                            // 表格后添加空段落分隔
                                            mergedDoc.createParagraph();
                                        }
                                    } catch (Exception tableError) {
                                        // 表格处理失败时仅记录错误，继续处理其他内容
                                        XWPFParagraph errorPara = mergedDoc.createParagraph();
                                        XWPFRun errorRun = errorPara.createRun();
                                        errorRun.setText("[警告] 文件 " + fileName + " 中的表格处理失败，已跳过表格内容");
                                    }
                                }
                            } catch (Exception docError) {
                                // 处理.doc文件时的错误
                                String errorMsg = docError.getMessage();
                                // 针对特定错误提供更友好的提示
                                if (errorMsg != null && errorMsg.contains("no such entry: \"1Table\"")) {
                                    errorMsg = "不支持的文档格式（可能是加密或特殊格式的.doc文件）";
                                }

                                XWPFParagraph errorPara = mergedDoc.createParagraph();
                                XWPFRun errorRun = errorPara.createRun();
                                errorRun.setText("[错误] 无法读取文件内容: " + fileName + " (原因: " + errorMsg + ")");
                            }
                        } else {
                            throw new RuntimeException("不支持的文件类型: " + filePath);
                        }

                        // 添加分页符（非最后一个文件）
                        if (i != fileList.size() - 1) {
                            mergedDoc.createParagraph().createRun().addBreak(BreakType.PAGE);
                        }
                    }
                } catch (Exception e) {
                    // 记录错误但继续处理其他文件
                    XWPFParagraph errorPara = mergedDoc.createParagraph();
                    XWPFRun errorRun = errorPara.createRun();
                    errorRun.setText("[错误] 处理文件时出错: " + fileName + " (错误: " + e.getMessage() + ")");

                    // 添加分页符继续处理下一个文件
                    if (i != fileList.size() - 1) {
                        mergedDoc.createParagraph().createRun().addBreak(BreakType.PAGE);
                    }
                }
            }

            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=merged_files.docx");
            mergedDoc.write(response.getOutputStream());
            response.getOutputStream().flush();
        } catch (Exception e) {
            throw new RuntimeException("文件合并失败: " + e.getMessage(), e);
        }
    }

    private void copyDocumentContent(XWPFDocument sourceDoc, XWPFDocument targetDoc) {
        // 复制段落
        for (XWPFParagraph para : sourceDoc.getParagraphs()) {
            XWPFParagraph newPara = targetDoc.createParagraph();
            copyParagraph(para, newPara);
        }

        // 复制表格
        for (XWPFTable table : sourceDoc.getTables()) {
            XWPFTable newTable = targetDoc.createTable(table.getRows().size(), table.getRow(0).getTableCells().size());
            copyTable(table, newTable);
        }
    }

    private void copyParagraph(XWPFParagraph source, XWPFParagraph target) {
        // 复制段落样式
        if (source.getCTP().getPPr() != null) {
            target.getCTP().setPPr(source.getCTP().getPPr());
        }
        // 复制段落中的文本运行
        for (XWPFRun run : source.getRuns()) {
            XWPFRun newRun = target.createRun();
            newRun.setText(run.getText(0));
            // 复制运行样式
            if (run.getCTR().getRPr() != null) {
                newRun.getCTR().setRPr(run.getCTR().getRPr());
            }
        }
    }

    private void copyTable(XWPFTable source, XWPFTable target) {
        // 复制表格样式
        if (source.getCTTbl().getTblPr() != null) {
            target.getCTTbl().setTblPr(source.getCTTbl().getTblPr());
        }
        // 复制行和单元格
        for (int rowIdx = 0; rowIdx < source.getRows().size(); rowIdx++) {
            XWPFTableRow sourceRow = source.getRow(rowIdx);
            XWPFTableRow targetRow = target.getRow(rowIdx);

            // 确保目标行存在
            if (targetRow == null) {
                targetRow = target.createRow();
            }

            for (int cellIdx = 0; cellIdx < sourceRow.getTableCells().size(); cellIdx++) {
                XWPFTableCell sourceCell = sourceRow.getCell(cellIdx);
                XWPFTableCell targetCell = targetRow.getCell(cellIdx);

                // 确保目标单元格存在
                if (targetCell == null) {
                    targetCell = targetRow.createCell();
                }

                // 清除目标单元格默认段落
                targetCell.removeParagraph(0);

                // 复制单元格内容
                for (XWPFParagraph cellPara : sourceCell.getParagraphs()) {
                    XWPFParagraph newPara = targetCell.addParagraph();
                    copyParagraph(cellPara, newPara);
                }

                // 复制单元格样式
                if (sourceCell.getCTTc().getTcPr() != null) {
                    targetCell.getCTTc().setTcPr(sourceCell.getCTTc().getTcPr());
                }
            }
        }
    }
}
