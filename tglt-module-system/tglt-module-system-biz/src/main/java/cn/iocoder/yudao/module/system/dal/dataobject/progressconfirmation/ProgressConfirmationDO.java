package cn.iocoder.yudao.module.system.dal.dataobject.progressconfirmation;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 进度确认 DO
 *
 * <AUTHOR>
 */
@TableName("zcd_progress_confirmation")
@KeySequence("zcd_progress_confirmation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProgressConfirmationDO extends BaseDO {

    /**
     * 序号
     */
    @TableId
    private Long id;
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目编号
     */
    private String projectCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户OID
     */
    private String customerOid;
    /**
     * 时段内工时
     */
    private Double duringTheTimePeriod;
    /**
     * 确认工时
     */
    private Double confirmWorkingHours;
    /**
     * 累计工时
     */
    private Double accumulatedWorkingHours;
    /**
     * 工时完成比
     */
    private String completionPercentage;
    /**
     * 项目节点id
     */
    private Long projectNodeId;
    /**
     * 交付表id
     */
    private Long deliveryId;
    /**
     * 备注
     */
    private String notes;
    /**
     * 开启状态
     */
    private Integer status;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 单节点名称
     */
    private String nodeName;
    /**
     * 单节点编号
     */
    private String nodeCode;
    /**
     * 当前状态:字典类型
     */
    private String currentStatus;

}