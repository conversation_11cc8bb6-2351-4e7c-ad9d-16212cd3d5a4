package cn.iocoder.yudao.module.system.service.deliveryinfo;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.*;
import java.util.stream.Collectors;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.system.controller.admin.deliveryinfo.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.deliveryinfo.DeliveryInfoDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.system.dal.mysql.deliveryinfo.DeliveryInfoMapper;
import cn.iocoder.yudao.module.system.dal.mysql.projectinfo.ProjectInfoMapper;
import cn.iocoder.yudao.module.system.dal.mysql.projecttimenode.ProjectTimeNodeMapper;
import cn.iocoder.yudao.module.system.dal.dataobject.projectinfo.ProjectInfoDO;
import cn.iocoder.yudao.module.system.dal.dataobject.projecttimenode.ProjectTimeNodeDO;
import cn.iocoder.yudao.module.system.service.dict.DictDataService;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;
import org.springframework.util.StringUtils;

/**
 * 交付信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeliveryInfoServiceImpl implements DeliveryInfoService {

    @Resource
    private DeliveryInfoMapper deliveryInfoMapper;

    @Resource
    private ProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjectTimeNodeMapper projectTimeNodeMapper;

    @Resource
    private DictDataService dictDataService;

    @Override
    public Long createDeliveryInfo(DeliveryInfoSaveReqVO createReqVO) {
        // 插入
        DeliveryInfoDO deliveryInfo = BeanUtils.toBean(createReqVO, DeliveryInfoDO.class);

        // 处理文件列表JSON转换
        if (createReqVO.getFileList() != null && !createReqVO.getFileList().isEmpty()) {
            deliveryInfo.setFileList(JsonUtils.toJsonString(createReqVO.getFileList()));
        }

        // 处理链接列表JSON转换
        if (createReqVO.getLinkList() != null && !createReqVO.getLinkList().isEmpty()) {
            deliveryInfo.setLinkList(JsonUtils.toJsonString(createReqVO.getLinkList()));
        }

        deliveryInfoMapper.insert(deliveryInfo);
        // 返回
        return deliveryInfo.getId();
    }

    @Override
    public void updateDeliveryInfo(DeliveryInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateDeliveryInfoExists(updateReqVO.getId());
        // 更新
        DeliveryInfoDO updateObj = BeanUtils.toBean(updateReqVO, DeliveryInfoDO.class);

        // 处理文件列表JSON转换
        if (updateReqVO.getFileList() != null && !updateReqVO.getFileList().isEmpty()) {
            updateObj.setFileList(JsonUtils.toJsonString(updateReqVO.getFileList()));
        }

        // 处理链接列表JSON转换
        if (updateReqVO.getLinkList() != null && !updateReqVO.getLinkList().isEmpty()) {
            updateObj.setLinkList(JsonUtils.toJsonString(updateReqVO.getLinkList()));
        }

        deliveryInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeliveryInfo(Long id) {
        // 校验存在
        validateDeliveryInfoExists(id);
        // 删除
        deliveryInfoMapper.deleteById(id);
    }

    private void validateDeliveryInfoExists(Long id) {
        if (deliveryInfoMapper.selectById(id) == null) {
            throw exception(DELIVERY_INFO_NOT_EXISTS);
        }
    }

    @Override
    public DeliveryInfoDO getDeliveryInfo(Long id) {
        return deliveryInfoMapper.selectById(id);
    }

    @Override
    public DeliveryInfoRespVO getDeliveryInfoWithFileList(Long id) {
        DeliveryInfoDO deliveryInfo = deliveryInfoMapper.selectById(id);
        if (deliveryInfo == null) {
            return null;
        }

        // 先创建VO对象，手动设置字段，避免BeanUtils转换fileList字段
        DeliveryInfoRespVO respVO = new DeliveryInfoRespVO();
        respVO.setId(deliveryInfo.getId());
        respVO.setProjectCode(deliveryInfo.getProjectCode());
        respVO.setProjectName(deliveryInfo.getProjectName());
        respVO.setProjectId(deliveryInfo.getProjectId());
        respVO.setCustomerName(deliveryInfo.getCustomerName());
        respVO.setCustomerOid(deliveryInfo.getCustomerOid());
        respVO.setAssociatedNodeCode(deliveryInfo.getAssociatedNodeCode());
        respVO.setAssociatedNodeName(deliveryInfo.getAssociatedNodeName());
        respVO.setDeliveryTime(deliveryInfo.getDeliveryTime());
        respVO.setDeliveryType(deliveryInfo.getDeliveryType());
        respVO.setDeliverableName(deliveryInfo.getDeliverableName());
        respVO.setDeliveryQuantity(deliveryInfo.getDeliveryQuantity());
        respVO.setDeliveryManHours(deliveryInfo.getDeliveryManHours());
        respVO.setCurrentStatus(deliveryInfo.getCurrentStatus());
        respVO.setDeliveryFileName(deliveryInfo.getDeliveryFileName());
        respVO.setRemarks(deliveryInfo.getRemarks());
        respVO.setStatus(deliveryInfo.getStatus());
        respVO.setCreateTime(deliveryInfo.getCreateTime());
        respVO.setDeptId(deliveryInfo.getDeptId());
        respVO.setFileName(deliveryInfo.getFileName());
        respVO.setFileUrl(deliveryInfo.getFileUrl());

        // 处理文件列表JSON反序列化
        if (deliveryInfo.getFileList() != null && !deliveryInfo.getFileList().trim().isEmpty()) {
            List<DeliveryFileInfoVO> fileList = JsonUtils.parseObject(
                deliveryInfo.getFileList(),
                new TypeReference<List<DeliveryFileInfoVO>>() {}
            );
            respVO.setFileList(fileList);
        } else {
            respVO.setFileList(new ArrayList<>());
        }

        // 处理链接列表JSON反序列化
        if (deliveryInfo.getLinkList() != null && !deliveryInfo.getLinkList().trim().isEmpty()) {
            List<LinkInfoVO> linkList = JsonUtils.parseObject(
                deliveryInfo.getLinkList(),
                new TypeReference<List<LinkInfoVO>>() {}
            );
            respVO.setLinkList(linkList);
        } else {
            respVO.setLinkList(new ArrayList<>());
        }

        return respVO;
    }

    @Override
    public PageResult<DeliveryInfoDO> getDeliveryInfoPage(DeliveryInfoPageReqVO pageReqVO) {
        return deliveryInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<DeliveryInfoRespVO> getDeliveryInfoPageWithFileList(DeliveryInfoPageReqVO pageReqVO) {
        PageResult<DeliveryInfoDO> pageResult = deliveryInfoMapper.selectPage(pageReqVO);

        // 转换DO列表到VO列表，并处理文件列表
        List<DeliveryInfoRespVO> respVOList = new ArrayList<>();
        for (DeliveryInfoDO deliveryInfo : pageResult.getList()) {
            // 先创建VO对象，手动设置字段，避免BeanUtils转换fileList字段
            DeliveryInfoRespVO respVO = new DeliveryInfoRespVO();
            respVO.setId(deliveryInfo.getId());
            respVO.setProjectCode(deliveryInfo.getProjectCode());
            respVO.setProjectName(deliveryInfo.getProjectName());
            respVO.setProjectId(deliveryInfo.getProjectId());
            respVO.setCustomerName(deliveryInfo.getCustomerName());
            respVO.setCustomerOid(deliveryInfo.getCustomerOid());
            respVO.setAssociatedNodeCode(deliveryInfo.getAssociatedNodeCode());
            respVO.setAssociatedNodeName(deliveryInfo.getAssociatedNodeName());
            respVO.setDeliveryTime(deliveryInfo.getDeliveryTime());
            respVO.setDeliveryType(deliveryInfo.getDeliveryType());
            respVO.setDeliverableName(deliveryInfo.getDeliverableName());
            respVO.setDeliveryQuantity(deliveryInfo.getDeliveryQuantity());
            respVO.setDeliveryManHours(deliveryInfo.getDeliveryManHours());
            respVO.setCurrentStatus(deliveryInfo.getCurrentStatus());
            respVO.setDeliveryFileName(deliveryInfo.getDeliveryFileName());
            respVO.setRemarks(deliveryInfo.getRemarks());
            respVO.setStatus(deliveryInfo.getStatus());
            respVO.setCreateTime(deliveryInfo.getCreateTime());
            respVO.setDeptId(deliveryInfo.getDeptId());
            respVO.setFileName(deliveryInfo.getFileName());
            respVO.setFileUrl(deliveryInfo.getFileUrl());

            // 处理文件列表JSON反序列化
            if (deliveryInfo.getFileList() != null && !deliveryInfo.getFileList().trim().isEmpty()) {
                List<DeliveryFileInfoVO> fileList = JsonUtils.parseObject(
                    deliveryInfo.getFileList(),
                    new TypeReference<List<DeliveryFileInfoVO>>() {}
                );
                respVO.setFileList(fileList);
            } else {
                respVO.setFileList(new ArrayList<>());
            }

            // 处理链接列表JSON反序列化
            if (deliveryInfo.getLinkList() != null && !deliveryInfo.getLinkList().trim().isEmpty()) {
                List<LinkInfoVO> linkList = JsonUtils.parseObject(
                    deliveryInfo.getLinkList(),
                    new TypeReference<List<LinkInfoVO>>() {}
                );
                respVO.setLinkList(linkList);
            } else {
                respVO.setLinkList(new ArrayList<>());
            }

            respVOList.add(respVO);
        }

        return new PageResult<>(respVOList, pageResult.getTotal());
    }

    @Override
    public List<ProjectInfoWithTimeNodeRespVO> getProjectInfoWithTimeNodes() {
        // 查询所有项目信息
        List<ProjectInfoDO> projectInfoList = projectInfoMapper.selectList();

        // 查询所有时间节点信息
        List<ProjectTimeNodeDO> timeNodeList = projectTimeNodeMapper.selectList();

        // 按项目ID分组时间节点
        Map<Long, List<ProjectTimeNodeDO>> timeNodeMap = timeNodeList.stream()
                .collect(Collectors.groupingBy(ProjectTimeNodeDO::getProjectId));

        // 构建返回结果
        List<ProjectInfoWithTimeNodeRespVO> result = new ArrayList<>();
        for (ProjectInfoDO projectInfo : projectInfoList) {
            ProjectInfoWithTimeNodeRespVO respVO = new ProjectInfoWithTimeNodeRespVO();
            respVO.setId(projectInfo.getId());
            respVO.setProjectCode(projectInfo.getProjectCode());
            respVO.setProjectName(projectInfo.getProjectName());

            // 获取该项目的时间节点列表
            List<ProjectTimeNodeDO> projectTimeNodes = timeNodeMap.get(projectInfo.getId());
            if (projectTimeNodes != null && !projectTimeNodes.isEmpty()) {
                List<ProjectInfoWithTimeNodeRespVO.TimeNodeInfo> timeNodeInfoList = projectTimeNodes.stream()
                        .map(timeNode -> {
                            ProjectInfoWithTimeNodeRespVO.TimeNodeInfo timeNodeInfo = new ProjectInfoWithTimeNodeRespVO.TimeNodeInfo();
                            timeNodeInfo.setCodeName(timeNode.getCodeName());
                            timeNodeInfo.setCodeNumber(timeNode.getCodeNumber());
                            return timeNodeInfo;
                        })
                        .collect(Collectors.toList());
                respVO.setTimeNodeList(timeNodeInfoList);
            } else {
                respVO.setTimeNodeList(new ArrayList<>());
            }

            result.add(respVO);
        }

        return result;
    }

    @Override
    public ProjectTimeNodeListsRespVO getProjectTimeNodeLists(String customerName) {
        // 查询所有项目信息
        List<ProjectInfoDO> projectInfoList = projectInfoMapper.selectList();
        if (projectInfoList == null) {
            projectInfoList = new ArrayList<>();
        }

        // 查询所有时间节点信息
        List<ProjectTimeNodeDO> timeNodeList = projectTimeNodeMapper.selectList();
        if (timeNodeList == null) {
            timeNodeList = new ArrayList<>();
        }

        // 如果提供了客户名称，先过滤项目列表
        if (StringUtils.hasText(customerName)) {
            projectInfoList = projectInfoList.stream()
                    .filter(project -> project != null && project.getCustomerName() != null 
                            && project.getCustomerName().contains(customerName))
                    .collect(Collectors.toList());
            
            // 获取过滤后的项目ID列表
            List<Long> filteredProjectIds = projectInfoList.stream()
                    .map(ProjectInfoDO::getId)
                    .collect(Collectors.toList());
            
            // 过滤时间节点列表，只保留匹配的项目的节点
            timeNodeList = timeNodeList.stream()
                    .filter(node -> filteredProjectIds.contains(node.getProjectId()))
                    .collect(Collectors.toList());
        }

        // 构建返回结果
        ProjectTimeNodeListsRespVO result = new ProjectTimeNodeListsRespVO();

        // 构建项目列表
        List<ProjectTimeNodeListsRespVO.ProjectInfo> projectList = projectInfoList.stream()
                .filter(project -> project != null && project.getProjectCode() != null && !project.getProjectCode().trim().isEmpty()
                        && project.getProjectName() != null && !project.getProjectName().trim().isEmpty())
                .map(project -> {
                    ProjectTimeNodeListsRespVO.ProjectInfo projectInfo = new ProjectTimeNodeListsRespVO.ProjectInfo();
                    projectInfo.setName(project.getProjectName());
                    projectInfo.setCode(project.getProjectCode());
                    return projectInfo;
                })
                .distinct()
                .collect(Collectors.toList());
        result.setProjectList(projectList);

        // 构建节点列表
        List<ProjectTimeNodeListsRespVO.NodeInfo> nodeList = timeNodeList.stream()
                .filter(node -> node != null && node.getCodeName() != null && !node.getCodeName().trim().isEmpty()
                        && node.getCodeNumber() != null && !node.getCodeNumber().trim().isEmpty())
                .map(node -> {
                    ProjectTimeNodeListsRespVO.NodeInfo nodeInfo = new ProjectTimeNodeListsRespVO.NodeInfo();
                    nodeInfo.setName(node.getCodeName());
                    nodeInfo.setCode(node.getCodeNumber());
                    return nodeInfo;
                })
                .distinct()
                .collect(Collectors.toList());
        result.setNodeList(nodeList);

        return result;
    }

    @Override
    public List<CustomerProjectRespVO> getCustomerProjectData() {
        // 获取基础数据
        Map<String, String> customerOidMap = getCustomerOidMap();
        List<ProjectInfoDO> projectInfoList = projectInfoMapper.selectList();
        Map<String, List<ProjectInfoDO>> customerProjectMap = projectInfoList.stream()
                .filter(project -> project.getCustomerName() != null && !project.getCustomerName().trim().isEmpty())
                .collect(Collectors.groupingBy(ProjectInfoDO::getCustomerName));

        // 构建结果，使用并行流提高性能
        return customerOidMap.entrySet().parallelStream().map(entry -> {
            String customerLabel = entry.getKey();
            String customerOid = entry.getValue();

            CustomerProjectRespVO customerProjectRespVO = new CustomerProjectRespVO();
            customerProjectRespVO.setCustomerName(customerLabel);
            customerProjectRespVO.setCustomerOid(customerOid);
            customerProjectRespVO.setProjectName(customerLabel);

            // 获取该客户的项目列表
            List<ProjectInfoDO> customerProjects = customerProjectMap.get(customerLabel);
            List<CustomerProjectRespVO.ProjectInfo> projectInfoVOList;

            if (customerProjects != null && !customerProjects.isEmpty()) {
                projectInfoVOList = customerProjects.stream()
                    .map(project -> convertToProjectInfo(project, customerOid))
                    .collect(Collectors.toList());
            } else {
                projectInfoVOList = new ArrayList<>();
            }

            customerProjectRespVO.setProjectList(projectInfoVOList);
            return customerProjectRespVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CustomerProjectRespVO.ProjectInfo> getProjectList() {
        // 获取基础数据
        Map<String, String> customerOidMap = getCustomerOidMap();
        List<ProjectInfoDO> projectInfoList = projectInfoMapper.selectList();
        
        // 直接转换为ProjectInfo列表
        return projectInfoList.stream()
                .filter(project -> project.getCustomerName() != null && !project.getCustomerName().trim().isEmpty())
                .map(project -> {
                    String customerOid = customerOidMap.get(project.getCustomerName());
                    return convertToProjectInfo(project, customerOid);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取客户OID映射表
     *
     * @return 客户名称到OID的映射
     */
    private Map<String, String> getCustomerOidMap() {
        List<DictDataDO> dictDataList = dictDataService.getDictDataListByDictType("project_Registration_customer_list");
        return dictDataList.stream()
                .filter(dict -> dict.getLabel() != null && !dict.getLabel().trim().isEmpty())
                .collect(Collectors.toMap(
                    DictDataDO::getLabel,
                    DictDataDO::getValue,
                    (v1, v2) -> v1
                ));
    }

    /**
     * 将ProjectInfoDO转换为ProjectInfo VO对象
     *
     * @param project 项目信息DO
     * @param defaultCustomerOid 默认的客户OID（当项目中没有指定时使用）
     * @return ProjectInfo VO对象
     */
    private CustomerProjectRespVO.ProjectInfo convertToProjectInfo(ProjectInfoDO project, String defaultCustomerOid) {
        CustomerProjectRespVO.ProjectInfo projectInfo = new CustomerProjectRespVO.ProjectInfo();
        projectInfo.setProjectName(project.getProjectName());
        projectInfo.setProjectCode(project.getProjectCode());
        projectInfo.setCustomerName(project.getCustomerName());
        // 优先使用项目中的customerOid，如果为空则使用默认值
        projectInfo.setCustomerOid(project.getCustomerOid() != null ? project.getCustomerOid() : defaultCustomerOid);
        return projectInfo;
    }

}
