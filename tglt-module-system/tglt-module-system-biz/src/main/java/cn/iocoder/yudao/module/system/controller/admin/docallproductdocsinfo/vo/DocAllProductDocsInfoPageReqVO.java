package cn.iocoder.yudao.module.system.controller.admin.docallproductdocsinfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - (产品文档列表)文档板块-GXP文档-产品文档列表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DocAllProductDocsInfoPageReqVO extends PageParam {

    @Schema(description = "关联的全客户ID（关联zcd_doc_all_client_product_docs_infos表的id）", example = "8556")
    private Long allClientId;

    @Schema(description = "文档编号（唯一标识文档的编码）")
    private String docCode;

    @Schema(description = "版本号（文档的版本信息）")
    private String docVersion;

    @Schema(description = "文档名称（文档的完整名称）", example = "李四")
    private String docName;

    @Schema(description = "文档类型（如：SOP、报告、方案等）", example = "1")
    private String docType;

    @Schema(description = "起草时间（文档开始起草的时间）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "启用时间（文档正式启用的时间）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] launchTime;

    @Schema(description = "废止时间（文档废止的时间，未废止则为空）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] abolishTime;

    @Schema(description = "适用组织OID（适用组织的唯一标识符）", example = "19420")
    private String applyOrgOid;

    @Schema(description = "适用组织名称（适用组织的名称）", example = "赵六")
    private String applyOrgName;

    @Schema(description = "适用组织机构代码（适用组织的机构代码）")
    private String applyOrgCode;

    @Schema(description = "文档介绍（文档的详细介绍和说明）")
    private String docIntro;

    @Schema(description = "代替文档（被当前文档替代的旧文档）")
    private String referenceDoc;

    @Schema(description = "上传组织机构代码（上传文档的组织机构代码）")
    private String uploadOrgCode;

    @Schema(description = "上传组织名称（上传文档的组织名称）", example = "张三")
    private String uploadOrgName;

    @Schema(description = "上传人组织内编号（上传人在组织内部的编号）", example = "10146")
    private String uploaderId;

    @Schema(description = "上传人（文档上传者的姓名）")
    private String uploader;

    @Schema(description = "上传人角色（上传者在组织中的角色）")
    private String uploaderRole;

    @Schema(description = "上传组织OID（上传组织的唯一标识符）", example = "10502")
    private String uploadOrgOid;

    @Schema(description = "关键词（文档的关键词，多个关键词用逗号分隔）")
    private String keywords;

    @Schema(description = "当前状态（1-已发布，2-待审核，3-已驳回，4-已废止）", example = "2")
    private String currentStatus;

    @Schema(description = "文件id", example = "2856")
    private String fileId;

    @Schema(description = "文件URL（文档文件的存储路径）", example = "https://www.iocoder.cn")
    private String fileUrl;

    @Schema(description = "文件大小（文档文件的大小，单位字节）")
    private Long fileSize;

    @Schema(description = "文件类型（如：PDF、DOC、DOCX等）", example = "1")
    private String fileType;

    @Schema(description = "备注（其他需要说明的信息）", example = "你说的对")
    private String remark;

    @Schema(description = "显示顺序（数字越小越靠前）")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "部门ID", example = "19")
    private Long deptId;

    @Schema(description = "甲方名称", example = "甲方名称")
    private String partyName;

}