package cn.iocoder.yudao.module.system.convert.examquestion;

import cn.iocoder.yudao.module.system.controller.admin.examquestion.vo.ExamQuestionRespVO;
import cn.iocoder.yudao.module.system.controller.admin.examquestion.vo.ExamQuestionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.examquestion.ExamQuestionDO;
import cn.iocoder.yudao.module.system.dal.dataobject.examquestion.ExamQuestionOptionDO;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T15:12:18+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 1.8.0_452 (Temurin)"
)
public class ExamQuestionConvertImpl implements ExamQuestionConvert {

    @Override
    public ExamQuestionDO convert(ExamQuestionSaveReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        ExamQuestionDO.ExamQuestionDOBuilder examQuestionDO = ExamQuestionDO.builder();

        examQuestionDO.id( bean.getId() );
        examQuestionDO.paperId( bean.getPaperId() );
        examQuestionDO.questionNo( bean.getQuestionNo() );
        examQuestionDO.title( bean.getTitle() );
        examQuestionDO.stem( bean.getStem() );
        examQuestionDO.categoryId( bean.getCategoryId() );
        examQuestionDO.type( bean.getType() );
        examQuestionDO.contentType( bean.getContentType() );
        examQuestionDO.imgPath( bean.getImgPath() );
        examQuestionDO.difficultyLevel( bean.getDifficultyLevel() );
        examQuestionDO.score( bean.getScore() );
        examQuestionDO.tags( bean.getTags() );
        examQuestionDO.analysis( bean.getAnalysis() );
        examQuestionDO.correctAnswer( bean.getCorrectAnswer() );

        return examQuestionDO.build();
    }

    @Override
    public ExamQuestionRespVO convert(ExamQuestionDO bean) {
        if ( bean == null ) {
            return null;
        }

        ExamQuestionRespVO examQuestionRespVO = new ExamQuestionRespVO();

        examQuestionRespVO.setId( bean.getId() );
        examQuestionRespVO.setQuestionNo( bean.getQuestionNo() );
        examQuestionRespVO.setTitle( bean.getTitle() );
        examQuestionRespVO.setStem( bean.getStem() );
        examQuestionRespVO.setCategoryId( bean.getCategoryId() );
        examQuestionRespVO.setType( bean.getType() );
        examQuestionRespVO.setContentType( bean.getContentType() );
        examQuestionRespVO.setImgPath( bean.getImgPath() );
        examQuestionRespVO.setDifficultyLevel( bean.getDifficultyLevel() );
        examQuestionRespVO.setScore( bean.getScore() );
        examQuestionRespVO.setTags( bean.getTags() );
        examQuestionRespVO.setAnalysis( bean.getAnalysis() );
        examQuestionRespVO.setCorrectAnswer( bean.getCorrectAnswer() );
        examQuestionRespVO.setPaperId( bean.getPaperId() );
        examQuestionRespVO.setCreator( bean.getCreator() );
        examQuestionRespVO.setCreateTime( bean.getCreateTime() );
        examQuestionRespVO.setUpdater( bean.getUpdater() );
        examQuestionRespVO.setUpdateTime( bean.getUpdateTime() );
        examQuestionRespVO.setTenantId( bean.getTenantId() );

        return examQuestionRespVO;
    }

    @Override
    public ExamQuestionRespVO.QuestionOption convertOption(ExamQuestionOptionDO bean) {
        if ( bean == null ) {
            return null;
        }

        ExamQuestionRespVO.QuestionOption questionOption = new ExamQuestionRespVO.QuestionOption();

        questionOption.setId( bean.getId() );
        questionOption.setOptionCode( bean.getOptionCode() );
        questionOption.setContent( bean.getContent() );
        questionOption.setImgPath( bean.getImgPath() );
        questionOption.setSort( bean.getSort() );

        return questionOption;
    }
}
